<div class="date-time-picker-container">
  <!-- Calendar Section -->
  <div class="calendar-section">
    <ava-calendar
      [isRange]="isRange"
      [selectedDate]="selectedDate"
      [dateRange]="dateRange"
      [alwaysOpen]="alwaysOpen"
      [weekdayFormat]="weekdayFormat"
      [selectorShape]="selectorShape"
      [surface]="surface"
      [surfaceStrength]="surfaceStrength"
      (dateSelected)="onDateSelected($event)"
      (rangeSelected)="onRangeSelected($event)">
    </ava-calendar>
  </div>

  <!-- Divider -->
  <div class="divider"></div>

  <!-- Time Section -->
  <div class="time-section">
    <div class="time-label">Select time</div>
    <ava-time-picker
      (timeSelected)="onTimeSelected($event)">
    </ava-time-picker>
  </div>
</div>
