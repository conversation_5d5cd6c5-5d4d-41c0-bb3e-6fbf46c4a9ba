import { CommonModule, NgIf } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  ElementRef,
  TemplateRef,
  ViewChild,
  AfterViewInit,
  ViewEncapsulation
} from '@angular/core';

@Component({
  selector: 'ava-select-option',
  imports: [CommonModule, NgIf],
  templateUrl: './select-option.component.html',
  styleUrls: ['./select-option.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SelectOptionComponent implements AfterViewInit {
  @Input() value: any;
  @Input() selected = false;
  @Output() optionSelected = new EventEmitter<any>();
  @Input() visible: boolean = true;

  @ViewChild('optionTpl', { static: true }) templateRef!: TemplateRef<any>;
  @ViewChild('contentWrapper') contentWrapper!: ElementRef;

  //label = '';

  constructor(public elementRef: ElementRef) { }
  get label(): string {
    return this.elementRef.nativeElement.textContent.trim().toLowerCase();
  }
  ngAfterViewInit() {
    // // Defer label extraction to next tick so contentWrapper is ready
    // setTimeout(() => {
    //   if (this.contentWrapper) {
    //     this.label = this.contentWrapper.nativeElement.textContent.trim();
    //   }
    // });
  }

  onClick() {
    this.optionSelected.emit(this.value);
  }
}
