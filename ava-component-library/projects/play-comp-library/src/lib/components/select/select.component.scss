.ava-select-option {

    .ava-select__label {
        display: block;
        font: var(--select-label-font);
        color: var(--select-label-color);
        margin-bottom: var(--select-label-margin);
        font-weight: var(--select-label-weight);

        &--required::after {
            content: "";
        }
    }

    .ava-select__required {
        color: var(--select-required-color);
        margin-left: 0.25rem;
    }



    .ava-select-container {
        cursor: pointer;
        user-select: none;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--select-toggle-padding);
        background: var(--select-toggle-background);
        border: var(--select-toggle-background) solid 1px;
        border-radius: var(--select-toggle-border-radius);
        box-shadow: var(--select-box-shadow);

        &:hover {
            border-color: var(--select-brand-color);
        }

        &:focus {
            outline: none;
            border-color: var(--select-brand-color);
        }

        &.open {
            border-color: var(--select-brand-color);
            border: var(--select-brand-color) solid 2px;
        }

        .ava-select {
            .ava-select-placeholder {
                color: var(--select-place-holder-color);
            }
        }

        &.ava-select--error {
            border-color: var(--select-error-border);
        }

        &.ava-select--disabled {
            cursor: not-allowed;
            opacity: 0.6;
            background-color: var(--color-background-disabled, #f5f5f5);

            &:hover {
                border-color: var(--select-toggle-background);
            }

            .ava-select-placeholder {
                color: var(--select-disabled-text);
            }
        }

    }



    .select-option-container {
        position: relative;

        .ava-select-panel {
            position: absolute;
            margin-top: 4px;
            z-index: 1000;
            width: 100%;
            max-height: 300px;
            overflow-y: auto;
            background: var(--select-menu-background);
            border: var(--select-menu-border) solid 1px;
            border-radius: var(--select-menu-border-radius);
            box-shadow: var(--select-menu-shadow);
            transform: translateY(-8px);
            animation: dropdownSlideDown 0.2s ease forwards;
            cursor: pointer;
            font: var(--select-toggle-font);

            &.single {
                .option {
                    &.selected {
                        background: var(--select-brand-color);
                        color: var(--select-brand-text-color);
                        border-left-color: var(--select-brand-color);
                    }
                }

            }

            .option {
                display: flex;
                align-items: center;
                padding: var(--select-option-padding);
                cursor: pointer;
                transition: var(--select-item-transition);
                font: var(--select-item-font);
                color: var(--select-item-text);
                border-radius: 0;
                margin: 0;
                position: relative;
                min-height: var(--select-size-md-height);
                background: var(--select-item-background);
                border-left: 3px solid transparent;



                &:focus &.focused {
                    background: var(--select-item-background-hover);
                    color: var(--select-item-text-hover);
                }

                &:hover,
                &:focus,
                &.focused {
                    background: var(--dropdown-item-background-hover);
                    color: var(--dropdown-item-text-hover);
                }

                &.disabled {
                    cursor: not-allowed;
                    opacity: 0.5;
                    color: var(--select-disabled-text);

                    &:hover,
                    &:focus,
                    &.focused {
                        background: var(--select-item-background);
                        color: var(--select-disabled-text);
                        box-shadow: none;
                    }



                    &.selected {
                        background: var(--select-item-background);
                        color: --select-disabled-text;
                        border-left-color: transparent;
                    }






                }

                ava-icon {
                    color: currentColor;
                    transition: all 0.2s ease;
                    margin-right: 5px;
                    position: relative;
                    top: 1px;
                }


            }

        }

        .search-box {
            position: relative;
            padding: var(--select-toggle-padding);
            background: var(--select-search-background);

            input {
                width: 100%;
                height: var(--select-size-sm-height);
                padding: var(--select-search-padding);
                border: none;
                border-radius: var(--select-search-border-radius);
                font: var(--select-search-font);
                color: var(--search-input-text);
                background: transparent;
                box-sizing: border-box;

                &::placeholder {
                    color: var(--select-place-holder-color);
                }

                &:focus {
                    outline: none;
                    background: transparent;
                }
            }

            ava-icon {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                color: var(--select-place-holder-color);
                pointer-events: none;
            }


        }

        .no-results {
            padding: var(--select-item-padding);
            text-align: center;
            color: var(--select-place-holder-color);
            font: var(--select-item-font);
            font-style: italic;
        }


    }

    .ava-select__error {
        display: flex;
        align-items: flex-start;
        gap: var(--select-error-gap);
        color: var(--select-error-color);
        font-size: var(--select-error-font-size);
        line-height: 1.4;
        margin-top: .5rem;
    }

    .ava-select__error-icon {
        flex-shrink: 0;
        margin-top: var(--global-spacing-1);
    }

    .ava-select__error-text {
        flex: 1;
    }


}

@keyframes dropdownSlideDown {
    0% {
        opacity: 0;
        transform: translateY(-8px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}