<div class="ava-select-option">
    <label *ngIf="label" [for]="inputId" class="ava-select__label" [class.ava-select__label--required]="required">
        {{ label }}
        <span *ngIf="required" class="ava-select__required" aria-hidden="true">*</span>
    </label>
    <div class="ava-select-container" [class.open]="isOpen" (click)="toggleDropdown()" [attr.aria-expanded]="isOpen"
        [attr.aria-haspopup]="'listbox'" [attr.aria-label]="getDisplayText()" [attr.aria-invalid]="hasError"
        [class]="wrapperClasses">
        <div class="ava-select" [id]="inputId" [class]="inputClasses">
            <div class="ava-select-placeholder">
                {{ getDisplayText() }}
            </div>

        </div>
        <div class="ava-select-arrow">
            <ava-icon [iconName]="dropdownIcon" [iconSize]="16"
                [style.transform]="isOpen ? 'rotate(180deg)' : 'rotate(0deg)'"></ava-icon>
        </div>
    </div>
    <!-- Error Message -->
    <div *ngIf="hasError" [id]="errorId" class="ava-select__error" role="alert" aria-live="polite">
        <ava-icon iconName="alert-circle" [iconSize]="14" class="ava-select__error-icon" [cursor]="false"
            [disabled]="false" [iconColor]="'red'"></ava-icon>
        <span class="ava-select__error-text">{{ error }}</span>
    </div>
    <div class="select-option-container">
        <div class="ava-select-panel" *ngIf="isOpen" [class.single]="!multiple">
            <div class="search-box">
                <input type="text" (input)="onSearchChange($event)" placeholder="Search..." />
                <ava-icon [iconName]="'search'" [iconSize]="14"></ava-icon>
            </div>
            <ng-content></ng-content>
            <ng-template #noOptions>
                <div class="no-results">No options found</div>
            </ng-template>
        </div>
    </div>


</div>