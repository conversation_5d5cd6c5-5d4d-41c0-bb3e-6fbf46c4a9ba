import {
  Component, ContentChildren, QueryList, AfterContentInit,
  forwardRef, Input, HostListener, ChangeDetectionStrategy,
  ChangeDetectorRef,
  Output,
  EventEmitter,
  ViewEncapsulation
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { SelectOptionComponent } from './select-option/select-option.component';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'ava-select',
  templateUrl: './select.component.html',
  styleUrls: ['./select.component.scss'],
  imports: [CommonModule, IconComponent],
  providers: [{
    provide: NG_VALUE_ACCESSOR,
    useExisting: forwardRef(() => SelectComponent),
    multi: true
  }],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None

})
export class SelectComponent implements AfterContentInit, ControlValueAccessor {
  private valueSetFromOutside = false;
  @Input() placeholder = 'Select';
  @Input() label = '';
  @Input() id = '';
  @Input() required = false;
  @Input() dropdownIcon: string = 'chevron-down';
  @Input() error = '';
  @Input() disabled = false;
  @Input() multiple: boolean = false;
  displayText = this.placeholder;
  searchTerm: string = '';

  filteredOptions: SelectOptionComponent[] = [];

  @ContentChildren(SelectOptionComponent) options!: QueryList<SelectOptionComponent>;

  selectedValue: any[] = [];
  isOpen = false;
  private _inputId!: string;
  @Output() selectionChange = new EventEmitter<any>();
  constructor(private cdr: ChangeDetectorRef) { }

  onChange = (_: any) => { };
  onTouched = () => { };
  ngOnInit(): void {
    this._inputId = `ava-select-${Math.random().toString(36).substr(2, 9)}`;
  }

  ngAfterContentInit(): void {
    // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.options.forEach(option => {
        option.optionSelected.subscribe((val: any) => {
          this.selectValue(val);
        });
        // Initialize visibility to true for all options
        if (option.visible === undefined) {
          option.visible = true;
        }
      });

      // Only apply [selected] fallback if no ngModel/formControl has set a value
      if (!this.valueSetFromOutside) {
        const defaultSelectedOptions = this.options.filter(opt => opt.selected);
        if (defaultSelectedOptions.length > 0) {
          if (this.multiple) {
            this.selectedValue = defaultSelectedOptions.map(opt => opt.value);
            this.onChange(this.selectedValue);
          } else {
            // For single select, only take the first selected option and clear others
            this.selectedValue = [defaultSelectedOptions[0].value];
            // Clear all selections first
            this.options.forEach(option => {
              option.selected = false;
            });
            // Set only the first one as selected
            defaultSelectedOptions[0].selected = true;
            this.onChange(defaultSelectedOptions[0].value);
          }
        }
      }
      this.updateSelectedStates();
      this.filteredOptions = this.options.toArray();
      this.cdr.markForCheck();
    });
  }
  toggleDropdown() {
    if (this.disabled) return;
    this.isOpen = !this.isOpen;
  }

  selectValue(val: any) {
    if (this.disabled) return;

    if (this.multiple) {
      const exists = this.selectedValue.some(v => this.compare(v, val));
      if (exists) {
        this.selectedValue = this.selectedValue.filter(v => !this.compare(v, val));
      } else {
        this.selectedValue = [...this.selectedValue, val];
      }
      this.markSelectedMultiple();
      this.onChange(this.selectedValue);
      this.selectionChange.emit(this.selectedValue);
    } else {
      // For single select, clear all selections first, then set the new one
      this.selectedValue = [val];
      // First clear all selections
      this.options?.forEach(option => {
        option.selected = false;
      });
      // Then set the selected one
      const selectedOption = this.options?.find(option => option.value === val);
      if (selectedOption) {
        selectedOption.selected = true;
      }
      this.onChange(val);
      this.selectionChange.emit(val);
      this.isOpen = false;
    }

    this.onTouched();
    this.cdr.detectChanges();
  }
  private compare(a: any, b: any): boolean {
    return a === b;
  }

  writeValue(val: any): void {
    this.valueSetFromOutside = true;
    if (this.multiple) {
      this.selectedValue = Array.isArray(val) ? val : [];
      setTimeout(() => this.markSelectedMultiple());
    } else {
      this.selectedValue = val ? [val] : [];
      setTimeout(() => this.markSelected(val));
    }
    this.cdr.markForCheck();
  }

  private markSelectedMultiple() {
    this.options?.forEach(option => {
      option.selected = this.selectedValue.some(val => this.compare(val, option.value));
    });
    // Force change detection for all options
    this.cdr.detectChanges();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  private markSelected(val: any) {
    if (!this.options) return;

    // First, explicitly clear all selections
    this.options.forEach(option => {
      option.selected = false;
    });

    // Then set only the selected one
    const selectedOption = this.options.find(option => option.value === val);
    if (selectedOption) {
      selectedOption.selected = true;
    }

    // Force change detection for all options
    this.cdr.detectChanges();
  }

  @HostListener('document:click', ['$event'])
  onOutsideClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('ava-select')) {
      this.isOpen = false;
    }
  }


  getDisplayText(): string {
    if (this.multiple && this.selectedValue.length) {
      return this.options
        .filter(option => this.selectedValue.includes(option.value))
        .map(option => option.elementRef.nativeElement.textContent.trim())
        .join(', ');
    }

    if (!this.multiple && this.selectedValue.length) {
      const selected = this.options.find(opt => opt.value === this.selectedValue[0]);
      return selected?.elementRef.nativeElement.textContent.trim() || this.placeholder;
    }

    return this.placeholder;
  }


  private updateSelectedStates() {
    if (!this.options) return;
    if (this.multiple) {
      this.markSelectedMultiple();
    } else {
      // For single select, clear all first then set the selected one
      this.options.forEach(option => {
        option.selected = false;
      });

      if (this.selectedValue.length > 0) {
        const selectedOption = this.options.find(option => option.value === this.selectedValue[0]);
        if (selectedOption) {
          selectedOption.selected = true;
        }
      }
    }
    this.cdr.detectChanges();
  }


  get inputId(): string {
    return this.id ?? this._inputId;
  }

  get hasError(): boolean {
    return !!this.error;
  }
  get errorId(): string {
    return `${this.inputId}-error`;
  }

  get inputClasses(): string {
    const classes = ['ava-select__input'];
    if (this.hasError) classes.push('ava-select__input--error');
    if (this.disabled) classes.push('ava-select__input--disabled');
    return classes.join(' ');
  }

  get wrapperClasses(): string {
    const classes = [];
    if (this.hasError) classes.push('ava-select--error');
    if (this.disabled) classes.push('ava-select--disabled');

    return [...classes].join(' ');
  }


  onSearchChange(event: Event) {
    const input = event.target as HTMLInputElement;
    this.searchTerm = input.value.toLowerCase();

    // Update visibility immediately but trigger change detection properly
    this.options.forEach(option => {
      const shouldBeVisible = option.label.toLowerCase().includes(this.searchTerm);
      if (option.visible !== shouldBeVisible) {
        option.visible = shouldBeVisible;
      }
    });

    // Mark for check to update the view
    this.cdr.detectChanges();
  }

}
