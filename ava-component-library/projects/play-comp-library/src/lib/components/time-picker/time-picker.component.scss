$timepicker-display-placeholder: var(--color-text-placeholder);
$timepicker-time-item-selected-text: var(--color-brand-primary);
$timepicker-shadow: var(--global-elevation-02);
$timepicker-border: 1px solid #e5e7eb;
// Gap variables for spacing
$timepicker-gap-hours-minutes: 3px;
$timepicker-gap-minutes-period: 10px;

.time-picker-container {
  position: relative;

  .time-picker-input {
    display: flex;
    align-items: center;
    border: $timepicker-border;
    border-radius: var(--timepicker-border-radius);
    box-shadow: $timepicker-shadow;
    cursor: pointer;
    height: 48px;
    width: 100%;
    min-width: 200px;
    transition: var(--timepicker-time-item-transition);
    outline: none;
    padding-left: $timepicker-gap-minutes-period;
    padding-right: $timepicker-gap-minutes-period;
    background: var(--timepicker-background);

    .time-display {
      flex: 1;
      text-align: left;
      font: var(--timepicker-display-font);
      color: var(--timepicker-display-text);
      cursor: pointer;
      transition: var(--timepicker-time-item-transition);

      &:hover {
        opacity: 0.8;
      }
    }



    .time-scroll-container {
      display: flex;
      align-items: center;
      flex: 1; // Take remaining space for scroll mode
      height: 100%;
      position: relative;
      z-index: 1;
      outline: none;
      background: var(--timepicker-scroll-background);
      gap: $timepicker-gap-hours-minutes; // Use gap for consistent spacing

      // Inline input overlay for scroll mode
      .inline-input-overlay {
        position: absolute;
        z-index: 10;
        background: var(--timepicker-input-background);
        border: 1px solid var(--color-brand-primary);
        border-radius: 4px;
        padding: 1px;
        box-shadow: var(--global-elevation-03);
        height: var(--timepicker-time-item-height);
        display: flex;
        align-items: center;
        justify-content: center;

        .inline-scroll-input {
          border: none;
          outline: none;
          background: var(--timepicker-input-background);
          text-align: center;
          font: var(--timepicker-time-item-font);
          color: var(--timepicker-input-text);
          width: 100%;
          height: 100%;
        }
      }

      .separator {
        font: var(--timepicker-time-item-font);
        margin: 0;
        flex-shrink: 0;

        &:last-of-type {
          margin-left: $timepicker-gap-minutes-period - $timepicker-gap-hours-minutes; // Extra space before AM/PM
        }
      }

      .time-scroll-column {
        flex: 1;
        height: 100%;
        position: relative;

        &.period-column {
          flex: 0.8; // Smaller width for AM/PM
        }

        .scroll-area {
          height: 100%;
          overflow-y: auto;
          scroll-behavior: smooth;
          max-height: var(--timepicker-scroll-max-height);
          scrollbar-width: none;
          -ms-overflow-style: none;

          &::-webkit-scrollbar {
            display: none;
          }

          .time-item {
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font: var(--timepicker-time-item-font);
            cursor: pointer;
            transition: var(--timepicker-time-item-transition);
            color: var(--timepicker-time-item-text);
            background: var(--timepicker-time-item-background);
            padding: var(--timepicker-time-item-padding);
            border-radius: var(--timepicker-time-item-border-radius);

            &:hover {
              background-color: var(--timepicker-time-item-hover-background);
              color: var(--timepicker-time-item-hover-text);
            }

            &.selected {
              color: $timepicker-time-item-selected-text;
              font-weight: 500;
            }

            &.padding-item {
              pointer-events: none;
              opacity: 0;
              cursor: default;
            }

            &.hidden-item {
              visibility: hidden;
            }
          }
        }
      }
    }

    .icon-wrapper {
      margin-left: var(--global-spacing-3);
      flex-shrink: 0;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: var(--timepicker-icon-background);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: var(--timepicker-time-item-transition);
      cursor: pointer;
      position: relative;
      outline: none;
      padding: var(--timepicker-icon-padding);

      ava-icon {
        pointer-events: none;
        color: var(--timepicker-icon-color);
      }
    }
  }
}