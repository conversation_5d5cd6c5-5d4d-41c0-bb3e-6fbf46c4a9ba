import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, EventEmitter, HostListener, Output, ViewChild } from '@angular/core';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'ava-time-picker',
  imports: [CommonModule,IconComponent],
  templateUrl: './time-picker.component.html',
  styleUrl: './time-picker.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TimePickerComponent implements AfterViewInit {
  @ViewChild('hoursScroll') hoursScroll!: ElementRef;
  @ViewChild('minutesScroll') minutesScroll!: ElementRef;
  @ViewChild('periodScroll') periodScroll!: ElementRef;

  @Output() timeSelected = new EventEmitter<string>();

  isFocused = false;
  hours: string = '';
  minutes: string = '';
  period: string = '';
  displayTime: string = '';

  // New properties for inline input in scroll mode
  showInlineInput = false;
  inlineInputType: 'hours' | 'minutes' | 'period' | null = null;
  inlineInputValue = '';
  inlineInputPosition = { top: 0, left: 0, width: 0 };
  clickedItemValue = ''; // To track which item should be hidden

  // Properties to track which items are currently centered (for visual selection)
  centeredHour: string = '';
  centeredMinute: string = '';
  centeredPeriod: string = '';

  // Original lists without padding
  private readonly originalHoursList: string[] = Array.from({ length: 12 }, (_, i) => (i + 1).toString().padStart(2, '0'));
  private readonly originalMinutesList: string[] = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  private readonly originalPeriodList: string[] = ['AM', 'PM'];

  // Number of padding items to add above and below for centering
  private readonly paddingCount = 2;

  // Generate arrays for scrolling with padding for centering
  readonly hoursList: string[];
  readonly minutesList: string[];
  readonly periodList: string[];

  private readonly scrollTimeouts: { [key: string]: any } = {};

  private generatePaddedList(originalList: string[]): string[] {
    const paddingItems = Array(this.paddingCount).fill('');
    return [...paddingItems, ...originalList, ...paddingItems];
  }

  constructor(private elementRef: ElementRef) {
    // Initialize padded lists in constructor
    this.hoursList = this.generatePaddedList(this.originalHoursList);
    this.minutesList = this.generatePaddedList(this.originalMinutesList);
    this.periodList = this.generatePaddedList(this.originalPeriodList);
  }

  ngOnInit() {
    this.updateDisplayTime();
    // Initialize centered values to match current values
    this.centeredHour = this.hours;
    this.centeredMinute = this.minutes;
    this.centeredPeriod = this.period;
    // Don't emit initial time since we start with empty values
  }

  ngAfterViewInit() {
    // Add wheel event listeners to enforce scroll boundaries
    if (this.hoursScroll?.nativeElement) {
      this.hoursScroll.nativeElement.addEventListener('wheel', (e: WheelEvent) => {
        this.handleWheelEvent(e, 'hours');
      });
    }
    if (this.minutesScroll?.nativeElement) {
      this.minutesScroll.nativeElement.addEventListener('wheel', (e: WheelEvent) => {
        this.handleWheelEvent(e, 'minutes');
      });
    }
    if (this.periodScroll?.nativeElement) {
      this.periodScroll.nativeElement.addEventListener('wheel', (e: WheelEvent) => {
        this.handleWheelEvent(e, 'period');
      });
    }
  }

  ngOnDestroy() {
    // Clean up any pending timeouts
    Object.values(this.scrollTimeouts).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });
  }

  onDisplayClick(event: Event) {
    event.stopPropagation(); // Prevent any parent click handlers

    // When clicking on time display, show scroll mode
    this.isFocused = true;

    // Set default values if empty
    if (!this.hours) this.hours = '01';
    if (!this.minutes) this.minutes = '00';
    if (!this.period) this.period = 'AM';

    // Center the current values in the scroll containers
    setTimeout(() => {
      this.centerScrollToValue('hours', this.hours);
      this.centerScrollToValue('minutes', this.minutes);
      this.centerScrollToValue('period', this.period);
      // Update display and emit after setting defaults
      this.updateDisplayTime();
      this.emitTimeSelected();
    }, 0);
  }



  onIconClick(event: Event) {
    event.stopPropagation(); // Prevent field click

    this.isFocused = true;

    // Set default values if empty
    if (!this.hours) this.hours = '01';
    if (!this.minutes) this.minutes = '00';
    if (!this.period) this.period = 'AM';

    // Center the current values in the scroll containers
    setTimeout(() => {
      this.centerScrollToValue('hours', this.hours);
      this.centerScrollToValue('minutes', this.minutes);
      this.centerScrollToValue('period', this.period);
      // Update display and emit after setting defaults
      this.updateDisplayTime();
      this.emitTimeSelected();
    }, 0);
  }



  autoSelectCurrentScrollValues() {
    // Force update all scroll columns to select the currently visible values
    if (this.hoursScroll?.nativeElement) {
      this.updateSelectionForColumn('hours');
    }
    if (this.minutesScroll?.nativeElement) {
      this.updateSelectionForColumn('minutes');
    }
    if (this.periodScroll?.nativeElement) {
      this.updateSelectionForColumn('period');
    }
  }





  onTimeItemClick(event: Event, type: 'hours' | 'minutes' | 'period', value: string) {
    event.stopPropagation(); // Prevent event bubbling

    // Get the position of the clicked element
    const clickedElement = event.target as HTMLElement;
    const rect = clickedElement.getBoundingClientRect();
    const containerRect = this.elementRef.nativeElement.querySelector('.time-scroll-container').getBoundingClientRect();

    // Calculate position relative to the scroll container
    this.inlineInputPosition = {
      top: rect.top - containerRect.top,
      left: rect.left - containerRect.left,
      width: rect.width
    };

    // Validate and set values properly
    if (type === 'hours') {
      this.hours = value.padStart(2, '0');
      this.centeredHour = value.padStart(2, '0');
    } else if (type === 'minutes') {
      this.minutes = value.padStart(2, '0');
      this.centeredMinute = value.padStart(2, '0');
    } else if (type === 'period') {
      this.period = value.toUpperCase() === 'AM' ? 'AM' : 'PM';
      this.centeredPeriod = value.toUpperCase() === 'AM' ? 'AM' : 'PM';
    }

    this.updateDisplayTime();
    this.emitTimeSelected();

    // NEW BEHAVIOR: Show inline input in scroll mode at clicked position
    this.showInlineInput = true;
    this.inlineInputType = type;
    this.inlineInputValue = value;
    this.clickedItemValue = value; // Track which item to hide

    setTimeout(() => {
      const inlineInput = document.querySelector('.inline-scroll-input') as HTMLInputElement;
      if (inlineInput) {
        inlineInput.focus();
        inlineInput.select();
      }
    }, 0);
  }











  // Handle scroll events with proper debouncing and isolation
  onScrollEvent(event: Event, type: 'hours' | 'minutes' | 'period') {
    event.stopPropagation();

    // Enforce scroll boundaries immediately
    this.enforceScrollBoundaries(type);

    // Clear any existing timeout for this specific column
    if (this.scrollTimeouts[type]) {
      clearTimeout(this.scrollTimeouts[type]);
    }

    // Set a new timeout for this specific column only
    this.scrollTimeouts[type] = setTimeout(() => {
      this.updateSelectionForColumn(type);
    }, 150); // Wait for scroll to settle
  }

  private enforceScrollBoundaries(type: 'hours' | 'minutes' | 'period') {
    let scrollContainer: ElementRef;
    let originalListLength: number;

    if (type === 'hours') {
      scrollContainer = this.hoursScroll;
      originalListLength = this.originalHoursList.length;
    } else if (type === 'minutes') {
      scrollContainer = this.minutesScroll;
      originalListLength = this.originalMinutesList.length;
    } else {
      scrollContainer = this.periodScroll;
      originalListLength = this.originalPeriodList.length;
    }

    if (!scrollContainer?.nativeElement) return;

    const itemHeight = 32;
    const containerHeight = scrollContainer.nativeElement.clientHeight;
    const currentScrollTop = scrollContainer.nativeElement.scrollTop;

    // Calculate boundaries to center first and last actual items
    // Minimum: scroll position to center the first actual item (index = paddingCount)
    const minScrollTop = (this.paddingCount * itemHeight) - (containerHeight / 2) + (itemHeight / 2);
    // Maximum: scroll position to center the last actual item (index = paddingCount + originalListLength - 1)
    const maxScrollTop = ((this.paddingCount + originalListLength - 1) * itemHeight) - (containerHeight / 2) + (itemHeight / 2);

    // Enforce minimum boundary (cannot scroll above first item centered)
    if (currentScrollTop < minScrollTop) {
      scrollContainer.nativeElement.scrollTop = minScrollTop;
      return;
    }

    // Enforce maximum boundary (cannot scroll below last item centered)
    if (currentScrollTop > maxScrollTop) {
      scrollContainer.nativeElement.scrollTop = maxScrollTop;
      return;
    }
  }

  private handleWheelEvent(event: WheelEvent, type: 'hours' | 'minutes' | 'period') {
    let scrollContainer: ElementRef;
    let originalListLength: number;

    if (type === 'hours') {
      scrollContainer = this.hoursScroll;
      originalListLength = this.originalHoursList.length;
    } else if (type === 'minutes') {
      scrollContainer = this.minutesScroll;
      originalListLength = this.originalMinutesList.length;
    } else {
      scrollContainer = this.periodScroll;
      originalListLength = this.originalPeriodList.length;
    }

    if (!scrollContainer?.nativeElement) return;

    const itemHeight = 32;
    const containerHeight = scrollContainer.nativeElement.clientHeight;
    const currentScrollTop = scrollContainer.nativeElement.scrollTop;
    const deltaY = event.deltaY;
    const newScrollTop = currentScrollTop + deltaY;

    // Calculate boundaries to center first and last actual items
    const minScrollTop = (this.paddingCount * itemHeight) - (containerHeight / 2) + (itemHeight / 2);
    const maxScrollTop = ((this.paddingCount + originalListLength - 1) * itemHeight) - (containerHeight / 2) + (itemHeight / 2);

    // Check if the new scroll position would exceed boundaries
    if (newScrollTop < minScrollTop || newScrollTop > maxScrollTop) {
      event.preventDefault(); // Prevent the scroll if it would exceed boundaries

      // Set to boundary value
      if (newScrollTop < minScrollTop) {
        scrollContainer.nativeElement.scrollTop = minScrollTop;
      } else {
        scrollContainer.nativeElement.scrollTop = maxScrollTop;
      }
    }
  }

  private updateSelectionForColumn(type: 'hours' | 'minutes' | 'period') {
    let scrollContainer: ElementRef;
    let list: string[];

    if (type === 'hours') {
      scrollContainer = this.hoursScroll;
      list = this.hoursList;
    } else if (type === 'minutes') {
      scrollContainer = this.minutesScroll;
      list = this.minutesList;
    } else {
      scrollContainer = this.periodScroll;
      list = this.periodList;
    }

    if (!scrollContainer?.nativeElement) return;

    const itemHeight = 32;
    const containerHeight = scrollContainer.nativeElement.clientHeight;
    const scrollTop = scrollContainer.nativeElement.scrollTop;
    const containerCenterY = containerHeight / 2;
    const scrollCenterPosition = scrollTop + containerCenterY;
    // Calculate which item is at the center with sticky selection
    // Find the item whose center is closest to the container center
    let bestIndex = 0;
    let minDistance = Infinity;

    // Add tolerance for sticky selection - once selected, need significant movement to change
    const tolerance = itemHeight * 0.3; // 30% of item height

    // First, check if current centered value is still reasonably centered
    let currentCenteredIndex = -1;
    if (type === 'hours' && this.centeredHour) {
      currentCenteredIndex = list.indexOf(this.centeredHour);
    } else if (type === 'minutes' && this.centeredMinute) {
      currentCenteredIndex = list.indexOf(this.centeredMinute);
    } else if (type === 'period' && this.centeredPeriod) {
      currentCenteredIndex = list.indexOf(this.centeredPeriod);
    }

    // If we have a current selection, check if it's still within tolerance
    if (currentCenteredIndex >= 0) {
      const currentItemCenterY = (currentCenteredIndex * itemHeight) + (itemHeight / 2);
      const currentDistance = Math.abs(currentItemCenterY - scrollCenterPosition);

      // If current item is still within tolerance, keep it selected
      if (currentDistance <= tolerance) {
        bestIndex = currentCenteredIndex;
      } else {
        // Current item is too far, find the new best one
        for (let i = 0; i < list.length; i++) {
          const itemCenterY = (i * itemHeight) + (itemHeight / 2);
          const distance = Math.abs(itemCenterY - scrollCenterPosition);

          if (distance < minDistance) {
            minDistance = distance;
            bestIndex = i;
          }
        }
      }
    } else {
      // No current selection, find the best one
      for (let i = 0; i < list.length; i++) {
        const itemCenterY = (i * itemHeight) + (itemHeight / 2);
        const distance = Math.abs(itemCenterY - scrollCenterPosition);

        if (distance < minDistance) {
          minDistance = distance;
          bestIndex = i;
        }
      }
    }

    const centeredValue = list[bestIndex];

    // Update the centered item for visual feedback (this determines which item gets black color)
    if (centeredValue && centeredValue.trim() !== '') {
      if (type === 'hours') {
        this.centeredHour = centeredValue;
        // Only update the actual selected value if it's perfectly centered
        this.hours = centeredValue;
      } else if (type === 'minutes') {
        this.centeredMinute = centeredValue;
        // Only update the actual selected value if it's perfectly centered
        this.minutes = centeredValue;
      } else if (type === 'period') {
        if (centeredValue === 'AM' || centeredValue === 'PM') {
          this.centeredPeriod = centeredValue;
          this.period = centeredValue;
        }
      }

      this.updateDisplayTime();
      this.emitTimeSelected();
    } else {
      if (type === 'hours') {
        this.centeredHour = '';
      } else if (type === 'minutes') {
        this.centeredMinute = '';
      } else if (type === 'period') {
        this.centeredPeriod = '';
      }
    }
  }



  centerScrollToValue(type: 'hours' | 'minutes' | 'period', value: string) {
    let scrollContainer: ElementRef;
    let itemIndex: number;
    let defaultValue: string;
    let originalList: string[];

    if (type === 'hours') {
      scrollContainer = this.hoursScroll;
      originalList = this.originalHoursList;
      defaultValue = value || '01'; 
      const originalIndex = originalList.indexOf(defaultValue);
      itemIndex = originalIndex >= 0 ? originalIndex + this.paddingCount : this.paddingCount;
           if (!value) {
        this.hours = defaultValue;
      }
    } else if (type === 'minutes') {
      scrollContainer = this.minutesScroll;
      originalList = this.originalMinutesList;
      defaultValue = value || '00'; 
      const originalIndex = originalList.indexOf(defaultValue);
      itemIndex = originalIndex >= 0 ? originalIndex + this.paddingCount : this.paddingCount;
       if (!value) {
        this.minutes = defaultValue;
      }
    } else {
      scrollContainer = this.periodScroll;
      originalList = this.originalPeriodList;
      defaultValue = value || 'AM'; 
      const originalIndex = originalList.indexOf(defaultValue);
      itemIndex = originalIndex >= 0 ? originalIndex + this.paddingCount : this.paddingCount;
      if (!value) {
        this.period = defaultValue;
      }
    }

    if (scrollContainer && itemIndex >= 0) {
      const itemHeight = 32; // Height of each item
      const containerHeight = scrollContainer.nativeElement.clientHeight;
      const scrollTop = (itemIndex * itemHeight) - (containerHeight / 2) + (itemHeight / 2);
      const originalListLength = originalList.length;
      const minScrollTop = (this.paddingCount * itemHeight) - (containerHeight / 2) + (itemHeight / 2);
      const maxScrollTop = ((this.paddingCount + originalListLength - 1) * itemHeight) - (containerHeight / 2) + (itemHeight / 2);
      const boundedScrollTop = Math.max(minScrollTop, Math.min(scrollTop, maxScrollTop));
      scrollContainer.nativeElement.scrollTop = boundedScrollTop;
    }
  }

  updateDisplayTime() {
    const { formattedTime } = this.getFormattedTime();
    this.displayTime = formattedTime;
  }

  emitTimeSelected() {
    const { formattedTime } = this.getFormattedTime();
    this.timeSelected.emit(formattedTime);
  }

  private getFormattedTime() {
    if (this.hours && this.minutes && this.period) {
      const hours = this.hours.padStart(2, '0');
      const minutes = this.minutes.padStart(2, '0');
      const period = this.period.toUpperCase();
      return {
        formattedTime: `${hours}:${minutes} ${period}`,
        hours,
        minutes,
        period
      };
    }
    return {
      formattedTime: 'HH:MM AM',
      hours: '',
      minutes: '',
      period: ''
    };
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const clickedElement = event.target as HTMLElement;
    const clickedInside = this.elementRef.nativeElement.contains(clickedElement);

    if (!clickedInside && this.isFocused) {
      this.autoSelectCurrentScrollValues();
      this.isFocused = false;
      this.closeInlineInput(); // Close inline input when clicking outside
    }
  }



  // New methods for inline input in scroll mode
  onInlineInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    let value = target.value;

    // Validate input based on type
    if (this.inlineInputType === 'hours') {
      // Only allow numbers, limit to 2 digits, and validate range
      value = value.replace(/[^0-9]/g, '').substring(0, 2);
      const numValue = parseInt(value, 10);

      // If it's a complete 2-digit number, validate range
      if (value.length === 2 && (numValue < 1 || numValue > 12)) {
        value = this.inlineInputValue; // Revert to previous valid value
      }
      // If it's a single digit, allow 1-9
      else if (value.length === 1 && (numValue < 1 || numValue > 9)) {
        value = this.inlineInputValue; // Revert to previous valid value
      }
    } else if (this.inlineInputType === 'minutes') {
      // Only allow numbers, limit to 2 digits, and validate range
      value = value.replace(/[^0-9]/g, '').substring(0, 2);
      const numValue = parseInt(value, 10);

      // If it's a complete 2-digit number, validate range
      if (value.length === 2 && numValue > 59) {
        value = this.inlineInputValue; // Revert to previous valid value
      }
    } else if (this.inlineInputType === 'period') {
      // Only allow A, P, M letters, case insensitive
      value = value.replace(/[^apmAPM]/g, '').substring(0, 2).toUpperCase();
    }

    this.inlineInputValue = value;
    target.value = value;
  }

  onInlineInputKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.applyInlineInput();
    } else if (event.key === 'Escape') {
      this.closeInlineInput();
    }
  }

  onInlineInputBlur() {
    this.applyInlineInput();
  }

  private applyInlineInput() {
    if (!this.inlineInputType || !this.inlineInputValue) {
      this.closeInlineInput();
      return;
    }

    let validatedValue = '';

    if (this.inlineInputType === 'hours') {
      validatedValue = this.validateInlineHours(this.inlineInputValue);
      if (validatedValue) {
        this.hours = validatedValue;
        this.centeredHour = validatedValue;
        this.centerScrollToValue('hours', validatedValue);
      }
    } else if (this.inlineInputType === 'minutes') {
      validatedValue = this.validateInlineMinutes(this.inlineInputValue);
      if (validatedValue) {
        this.minutes = validatedValue;
        this.centeredMinute = validatedValue;
        this.centerScrollToValue('minutes', validatedValue);
      }
    } else if (this.inlineInputType === 'period') {
      const periodValue = this.validateInlinePeriod(this.inlineInputValue);
      if (periodValue) {
        this.period = periodValue;
        this.centeredPeriod = periodValue;
        this.centerScrollToValue('period', periodValue);
      }
    }

    if (validatedValue || this.inlineInputType === 'period') {
      this.updateDisplayTime();
      this.emitTimeSelected();
    }

    this.closeInlineInput();
  }

  // New validation methods for inline input
  private validateInlineHours(value: string): string {
    if (!value) return '';

    const numValue = parseInt(value, 10);
    if (isNaN(numValue)) return '';

    // Handle single digit (1-9) - pad with zero
    if (value.length === 1) {
      if (numValue >= 1 && numValue <= 9) {
        return value.padStart(2, '0');
      }
      return '';
    }

    // Handle two digits (01-12)
    if (value.length === 2) {
      if (numValue >= 1 && numValue <= 12) {
        return value.padStart(2, '0');
      }
      return '';
    }

    return '';
  }

  private validateInlineMinutes(value: string): string {
    if (!value) return '';

    const numValue = parseInt(value, 10);
    if (isNaN(numValue)) return '';

    // Handle single digit (0-9) - pad with zero
    if (value.length === 1) {
      if (numValue >= 0 && numValue <= 9) {
        return value.padStart(2, '0');
      }
      return '';
    }

    // Handle two digits (00-59)
    if (value.length === 2) {
      if (numValue >= 0 && numValue <= 59) {
        return value.padStart(2, '0');
      }
      return '';
    }

    return '';
  }

  private validateInlinePeriod(value: string): string {
    if (!value) return '';

    const upperValue = value.toUpperCase();

    // Handle single letter
    if (upperValue.length === 1) {
      if (upperValue === 'A') return 'AM';
      if (upperValue === 'P') return 'PM';
      return '';
    }

    // Handle two letters
    if (upperValue === 'AM' || upperValue === 'PM') {
      return upperValue;
    }

    return '';
  }

  private closeInlineInput() {
    this.showInlineInput = false;
    this.inlineInputType = null;
    this.inlineInputValue = '';
    this.inlineInputPosition = { top: 0, left: 0, width: 0 };
    this.clickedItemValue = '';
  }
}
