<table>
    <thead>
        <tr>
            <ng-container *ngFor="let column of columns">
                <th (click)="onSort(column)" [style.cursor]="column.sortable ? 'pointer' : 'default'">
                    <div>
                        <ng-container *ngIf="column.headerCellDef">
                            <ng-container *ngTemplateOutlet="column.headerCellDef.templateRef"></ng-container>
                        </ng-container>
                        <ng-container>
                            <span class="sort-icon" *ngIf="sortColumn === column.name">
                                {{ sortDirection === 'asc' ? '▼' : sortDirection === 'desc' ? '▲' : ''}}
                            </span>
                            <span class="sort-icon" *ngIf="!sortDirection && column.sortable">
                                ▲
                            </span>
                        </ng-container>
                    </div>
                </th>
            </ng-container>
        </tr>
    </thead>
    <tbody>
        <tr *ngFor="let row of sortedData; let i = index">
            <ng-container *ngFor=" let column of columns">
                <td>
                    <ng-container *ngIf="column.cellDef" [ngTemplateOutlet]="column.cellDef.templateRef"
                        [ngTemplateOutletContext]="{ $implicit: row ,index: i}"></ng-container>
                </td>
            </ng-container>
        </tr>
    </tbody>
</table>