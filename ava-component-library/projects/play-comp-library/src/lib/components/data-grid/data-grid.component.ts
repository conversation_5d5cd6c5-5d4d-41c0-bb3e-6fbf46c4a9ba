import { CommonModule, NgFor } from '@angular/common';
import { ChangeDetectionStrategy, Component, ContentChild, ContentChildren, Directive, EventEmitter, Input, OnInit, Output, QueryList, TemplateRef } from '@angular/core';

import { AvaHeaderCellDefDirective } from './directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from './directive/ava-cell-def.directive';
import { AvaColumnDefDirective } from './directive/ava-column-def.directive';





@Component({
  selector: 'ava-data-grid',
  imports: [CommonModule, NgFor,
  ],
  templateUrl: './data-grid.component.html',
  styleUrl: './data-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DataGridComponent implements OnInit {
  @Input() dataSource: any[] = [];
  @Input() displayedColumns: string[] = [];

  @ContentChildren(AvaColumnDefDirective) columnDefs!: QueryList<AvaColumnDefDirective>;
  columns: AvaColumnDefDirective[] = [];
  //sorting 
  sortColumn: string | null = null;
  sortDirection: 'asc' | 'desc' | '' = '';
  sortedData: any[] = [];
  @Output() dataSorted = new EventEmitter<any[]>();
  ngOnInit() {
    this.sortedData = [...this.dataSource]
  }
  ngAfterContentInit() {
    console.log(this.displayedColumns);
    this.columns = this.displayedColumns
      .map(colName => {

        const col = this.columnDefs.find(col => col.name === colName);
        if (!col) {
          console.warn(`⚠️ Column with name '${colName}' was not found in <ng-container avaColumnDef="...">.`);
        }
        return col;
      })
      .filter((col): col is AvaColumnDefDirective => !!col);

    console.log('✅ Resolved Columns:', this.columns);
  }

  get debugColumns() {
    return this.columns.map(col => ({
      name: col.name,
      hasHeader: !!col.headerCellDef,
      hasCell: !!col.cellDef
    }));
  }


  onSort(column: AvaColumnDefDirective) {
    if (!column.sortable) return;

    const columnName = column.name;
    if (this.sortColumn !== columnName) {
      this.sortColumn = columnName;
      this.sortDirection = 'asc';
    } else {
      switch (this.sortDirection) {
        case 'asc':
          this.sortDirection = 'desc';
          break;
        case 'desc':
          this.sortDirection = 'asc';
          break;
        default:
          this.sortDirection = 'asc';
      }
    }
    this.applySort();
  }


  applySort() {
    if (!this.sortColumn || !this.sortDirection) {
      this.sortedData = [...this.dataSource];
    } else {
      this.sortedData = [...this.dataSource].sort((a, b) => {
        const valueA = a[this.sortColumn!];
        const valueB = b[this.sortColumn!];

        if (valueA == null && valueB == null) return 0;
        if (valueA == null) return -1;
        if (valueB == null) return 1;

        const result =
          typeof valueA === 'string' && typeof valueB === 'string'
            ? valueA.localeCompare(valueB)
            : valueA > valueB ? 1 : valueA < valueB ? -1 : 0;

        return this.sortDirection === 'asc' ? result : -result;
      });
    }

    this.dataSource = this.sortedData;
    this.dataSorted.emit(this.sortedData)
  }



}
