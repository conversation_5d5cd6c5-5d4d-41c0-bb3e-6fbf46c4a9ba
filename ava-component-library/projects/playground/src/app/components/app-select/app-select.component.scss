.button-demo-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;

    .demo-header {
      margin-bottom: 40px;
      padding: 40px 0;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .row {
      display: flex;
      flex-wrap: wrap;
      margin: -15px;
    }

    .col-12 {
      flex: 0 0 100%;
      max-width: 100%;
      padding: 15px;
    }

    .col-md-6 {
      flex: 0 0 50%;
      max-width: 50%;
      padding: 15px;

      @media (max-width: 768px) {
        flex: 0 0 100%;
        max-width: 100%;
      }
    }

    .box {
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      color: black;

      h1 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 24px;
        font-weight: 600;
      }

      h2 {
        margin: 0 0 15px 0;
        color: #555;
        font-size: 18px;
        font-weight: 500;
      }

      h4 {
        margin: 20px 0 10px 0;
        color: #555;
        font-size: 16px;
        font-weight: 500;
      }

      p {
        margin: 0 0 20px 0;
        color: #666;
        line-height: 1.5;
        font-size: 14px;
      }

      pre {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        font-size: 12px;
        overflow-x: auto;
        margin: 10px 0;
        color: black;
      }

      .mt-3 {
        margin-top: 1rem;
      }

      strong {
        color: #333;
        font-weight: 600;
      }

      small {
        font-size: 0.875em;
        color: #6B7280;
      }

      // Button spacing
      ava-button {
        margin-right: 10px;
        margin-bottom: 10px;
      }

      // Form styling
      form {
        .row {
          margin: -10px;
        }

        .col-md-6 {
          padding: 10px;
        }
      }
    }
}