<div class="button-demo-page">
    <!-- Header -->
    <div class="demo-header" style="background: #fff;">
        <div class="container">
            <div class="row">
                <!-- Basic Select -->
                <div class="col-12">
                    <div class="box container">
                        <h1>1. Basic Select</h1>
                        <p>Simple select component with default selection</p>
                        <ava-select label="Select User" placeholder="Select a user">
                            <ava-select-option *ngFor="let user of users" [value]="user.value"
                                [selected]="user.value === 1">
                                <ava-icon iconSize="15" iconName="user"></ava-icon> {{ user.label }}
                            </ava-select-option>
                        </ava-select>
                    </div>
                </div>

                <!-- NgModel Binding -->
                <div class="col-12">
                    <div class="box container">
                        <h1>2. NgModel Binding</h1>
                        <p>Two-way data binding with ngModel</p>
                        <ava-select label="Select User" placeholder="Select a user" [(ngModel)]="selectedUser">
                            <ava-select-option *ngFor="let user of users" [value]="user.value">
                                {{ user.label }}
                            </ava-select-option>
                        </ava-select>
                        <div class="mt-3">
                            <strong>Selected User ID:</strong> {{selectedUser}}
                        </div>
                    </div>
                </div>

                <!-- Form Control -->
                <div class="col-12">
                    <div class="box container">
                        <h1>3. Form Control Integration</h1>
                        <p>Reactive form integration with validation</p>
                        <form [formGroup]="form" (ngSubmit)="submit()">
                            <ava-select [required]="true" [error]="getFieldError('userId')" label="Select User"
                                (selectionChange)="selectionChange($event)" placeholder="Select a user"
                                formControlName="userId">
                                <ava-select-option *ngFor="let user of users" [value]="user.value">
                                    {{ user.label }}
                                </ava-select-option>
                            </ava-select>

                            <div class="mt-3">
                                <pre>{{ form.get('userId')?.value | json }}</pre>
                                <ava-button variant="primary" label="Submit" type="submit"></ava-button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Multiple Selection -->
                <div class="col-12">
                    <div class="box container">
                        <h1>4. Multiple Selection</h1>
                        <p>Select multiple options with checkboxes</p>
                        <ava-select [multiple]="true" label="Select Users" placeholder="Select users" [(ngModel)]="selectedUsers">
                            <ava-select-option *ngFor="let user of users" [value]="user.value">
                                <ava-checkbox variant="with-bg" [isChecked]="selectedUsers.includes(user.value)"
                                    [label]="user.label">
                                </ava-checkbox>
                            </ava-select-option>
                        </ava-select>
                        <div class="mt-3">
                            <strong>Selected Users:</strong> {{selectedUsers | json}}
                        </div>
                    </div>
                </div>

                <!-- With Icons -->
                <div class="col-12">
                    <div class="box container">
                        <h1>5. Options with Icons</h1>
                        <p>Select options with icons for better visual cues</p>
                        <ava-select label="Select Country" placeholder="Select a country" [(ngModel)]="selectedCountry">
                            <ava-select-option *ngFor="let country of countries" [value]="country.value">
                                <ava-icon iconSize="15" [iconName]="country.icon"></ava-icon> {{ country.label }}
                            </ava-select-option>
                        </ava-select>
                        <div class="mt-3">
                            <strong>Selected Country:</strong> {{selectedCountry}}
                        </div>
                    </div>
                </div>

                <!-- Multi-select with Form Control -->
                <div class="col-12">
                    <div class="box container">
                        <h1>6. Multi-select with Form Control</h1>
                        <p>Multiple selection with reactive form validation</p>
                        <form [formGroup]="multiSelectForm" (ngSubmit)="submitMultiSelect()">
                            <ava-select [multiple]="true" [required]="true"
                                [error]="getMultiSelectFieldError('languages')"
                                label="Programming Languages"
                                placeholder="Select programming languages"
                                formControlName="languages"
                                (selectionChange)="multiSelectionChange($event)">
                                <ava-select-option *ngFor="let lang of programmingLanguages" [value]="lang.value">
                                    <ava-checkbox variant="with-bg"
                                        [isChecked]="multiSelectForm.get('languages')?.value?.includes(lang.value)"
                                        [label]="lang.label + ' (' + lang.year + ')'">
                                    </ava-checkbox>
                                </ava-select-option>
                            </ava-select>

                            <div class="mt-3">
                                <pre>{{ multiSelectForm.get('languages')?.value | json }}</pre>
                                <ava-button variant="primary" label="Submit Multi-select" type="submit"></ava-button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Complex Options with Additional Info -->
                <div class="col-12">
                    <div class="box container">
                        <h1>7. Complex Options with Additional Info</h1>
                        <p>Options with icons and additional information</p>
                        <ava-select label="Select Team Member" placeholder="Select a team member" [(ngModel)]="selectedTeamMembers">
                            <ava-select-option *ngFor="let member of teamMembers" [value]="member.value">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <ava-icon iconSize="15" [iconName]="member.icon"></ava-icon>
                                    <div>
                                        <div>{{ member.label }}</div>
                                        <small style="color: #6B7280;">{{ member.role }}</small>
                                    </div>
                                </div>
                            </ava-select-option>
                        </ava-select>
                    </div>
                </div>

                <!-- Status with Color Indicators -->
                <div class="col-12">
                    <div class="box container">
                        <h1>8. Status with Color Indicators</h1>
                        <p>Options with colored icons to indicate status</p>
                        <ava-select label="Select Status" placeholder="Select status" [(ngModel)]="selectedStatus">
                            <ava-select-option *ngFor="let status of statusOptions" [value]="status.value">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <ava-icon iconSize="15" [iconName]="status.icon" [iconColor]="status.color"></ava-icon>
                                    {{ status.label }}
                                </div>
                            </ava-select-option>
                        </ava-select>
                        <div class="mt-3">
                            <strong>Selected Status:</strong> {{selectedStatus}}
                        </div>
                    </div>
                </div>

                <!-- Priority Levels -->
                <div class="col-12">
                    <div class="box container">
                        <h1>9. Priority Levels</h1>
                        <p>Priority selection with visual indicators</p>
                        <ava-select label="Select Priority" placeholder="Select priority level" [(ngModel)]="selectedPriority">
                            <ava-select-option *ngFor="let priority of priorityLevels" [value]="priority.value">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <ava-icon iconSize="15" [iconName]="priority.icon" [iconColor]="priority.color"></ava-icon>
                                    {{ priority.label }}
                                </div>
                            </ava-select-option>
                        </ava-select>
                    </div>
                </div>

                <!-- Searchable Select -->
                <div class="col-12">
                    <div class="box container">
                        <h1>10. Searchable Select (Large Dataset)</h1>
                        <p>Select with search functionality for large datasets</p>
                        <ava-select label="Search Items" placeholder="Search and select an item" [(ngModel)]="largeDataValue">
                            <ava-select-option *ngFor="let item of largeDataset" [value]="item.value">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <ava-icon iconSize="15" [iconName]="item.icon"></ava-icon>
                                    <div>
                                        <div>{{ item.label }}</div>
                                        <small style="color: #6B7280;">{{ item.category }}</small>
                                    </div>
                                </div>
                            </ava-select-option>
                        </ava-select>
                        <div class="mt-3">
                            <strong>Selected Item:</strong> {{largeDataValue}}
                        </div>
                    </div>
                </div>

                <!-- Disabled State -->
                <div class="col-12">
                    <div class="box container">
                        <h1>11. Disabled State</h1>
                        <p>Select component in disabled state</p>
                        <ava-select label="Disabled Select" placeholder="This select is disabled"
                            [disabled]="true" [(ngModel)]="disabledValue">
                            <ava-select-option *ngFor="let status of statusOptions" [value]="status.value">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <ava-icon iconSize="15" [iconName]="status.icon"></ava-icon>
                                    {{ status.label }}
                                </div>
                            </ava-select-option>
                        </ava-select>
                    </div>
                </div>

                <!-- Error State -->
                <div class="col-12">
                    <div class="box container">
                        <h1>12. Error State</h1>
                        <p>Select component with error validation</p>
                        <form [formGroup]="form">
                            <ava-select label="Select Status" placeholder="Select a status"
                                [required]="true" [error]="getFieldError('status')"
                                formControlName="status">
                                <ava-select-option *ngFor="let status of statusOptions" [value]="status.value">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <ava-icon iconSize="15" [iconName]="status.icon" [iconColor]="status.color"></ava-icon>
                                        {{ status.label }}
                                    </div>
                                </ava-select-option>
                            </ava-select>
                        </form>
                        <div class="mt-3">
                            <ava-button variant="secondary" label="Trigger Error" (click)="triggerError()"></ava-button>
                            <ava-button variant="success" label="Clear Error" (click)="clearError()"></ava-button>
                        </div>
                    </div>
                </div>

                <!-- Custom Dropdown Icon -->
                <div class="col-12">
                    <div class="box container">
                        <h1>13. Custom Dropdown Icon</h1>
                        <p>Select with custom dropdown icon</p>
                        <ava-select label="Custom Icon Select" placeholder="Select with custom icon"
                            dropdownIcon="settings" [(ngModel)]="customIconValue">
                            <ava-select-option *ngFor="let lang of programmingLanguages" [value]="lang.value">
                                <ava-icon iconSize="15" [iconName]="lang.icon"></ava-icon> {{ lang.label }}
                            </ava-select-option>
                        </ava-select>
                    </div>
                </div>

                <!-- All Features Combined -->
                <div class="col-12">
                    <div class="box container">
                        <h1>14. All Features Combined</h1>
                        <p>Comprehensive example with all select features</p>
                        <form [formGroup]="form">
                            <div class="row">
                                <div class="col-md-6">
                                    <ava-select label="Country" placeholder="Select country"
                                        [required]="true" [error]="getFieldError('countryId')"
                                        formControlName="countryId">
                                        <ava-select-option *ngFor="let country of countries" [value]="country.value">
                                            <ava-icon iconSize="15" [iconName]="country.icon"></ava-icon> {{ country.label }}
                                        </ava-select-option>
                                    </ava-select>
                                </div>
                                <div class="col-md-6">
                                    <ava-select label="Priority" placeholder="Select priority"
                                        formControlName="priority">
                                        <ava-select-option *ngFor="let priority of priorityLevels" [value]="priority.value">
                                            <ava-icon iconSize="15" [iconName]="priority.icon" [iconColor]="priority.color"></ava-icon>
                                            {{ priority.label }}
                                        </ava-select-option>
                                    </ava-select>
                                </div>
                            </div>

                            <div class="mt-3">
                                <h4>Form Values:</h4>
                                <pre>{{ form.value | json }}</pre>
                                <div class="mt-3">
                                    <ava-button variant="primary" label="Submit All" (click)="submit()"></ava-button>
                                    <ava-button variant="secondary" label="Reset Forms" (click)="resetForms()"></ava-button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>