<div class="button-demo-page">
    <!-- Header -->
    <div class="demo-header" style="background: #fff;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="box container">
                        <h1>Select Basic</h1>
                        <ava-select label="Select User" placeholder="Select a user">
                            <ava-select-option *ngFor="let user of users" [value]="user.value"
                                [selected]="user.value === 1">
                                <ava-icon iconSize="15" iconName="user"></ava-icon>{{ user.label }}

                            </ava-select-option>
                        </ava-select>
                    </div>
                </div>


                <div class="col-12">
                    <div class="box container">
                        <h1>Select NgModel</h1>
                        <ava-select label="Select User" placeholder="Select a user" [(ngModel)]="selectedUser">
                            <ava-select-option *ngFor="let user of users" [value]="user.value">
                                {{ user.label }}
                            </ava-select-option>
                        </ava-select>
                        Selected Use: {{selectedUser}}
                    </div>
                </div>




                <div class="col-12">
                    <div class="box container">
                        <h1>Form Control</h1>
                        <form [formGroup]="form" (ngSubmit)="submit()">
                            <ava-select [required]="true" [error]="getFieldError('userId')" label="Select User"
                                (selectionChange)="selectionChange($event)" placeholder="Select a user"
                                formControlName="userId">
                                <ava-select-option *ngFor="let user of users" [value]="user.value">
                                    {{ user.label }}
                                </ava-select-option>
                            </ava-select>


                            <pre>{{ form.value | json }}</pre>
                            <ava-button variant="primary" label="Submit"></ava-button>

                        </form>
                    </div>
                </div>


                <div class="col-12">
                    <div class="box container">
                        <h1>Select Multiple</h1>
                        <ava-select [multiple]="true" label="Select User" placeholder="Select a user">
                            <ava-select-option *ngFor="let user of users" [value]="user.value"
                                [selected]="user.value === 1 || user.value === 2">
                                <ava-checkbox variant="with-bg" [isChecked]="user.value === 1 || user.value === 2"
                                    [label]=" user.label">
                                </ava-checkbox>
                            </ava-select-option>
                        </ava-select>
                    </div>
                </div>







            </div>
        </div>
    </div>
</div>