import { Component } from '@angular/core';
import { SelectComponent } from '../../../../../play-comp-library/src/lib/components/select/select.component';
import { SelectOptionComponent } from '../../../../../play-comp-library/src/lib/components/select/select-option/select-option.component';
import { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule, NgFor } from '@angular/common';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ButtonComponent, IconComponent } from 'play-comp-library';
import { CheckboxComponent } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-app-select',
  imports: [CommonModule, IconComponent, ButtonComponent, FormsModule, CheckboxComponent, ReactiveFormsModule, NgFor, SelectComponent, SelectOptionComponent],
  templateUrl: './app-select.component.html',
  styleUrl: './app-select.component.scss'
})
export class AppSelectComponent {
  form: FormGroup;
  multiSelectForm: FormGroup;

  // Basic users data
  users = [
    { value: 1, label: 'John Doe' },
    { value: 2, label: 'Jane Smith' },
    { value: 3, label: 'Jack Wilson' },
    { value: 4, label: 'James Brown' }
  ];

  // Countries data with icons
  countries = [
    { value: 'us', label: 'United States', icon: 'flag' },
    { value: 'ca', label: 'Canada', icon: 'flag' },
    { value: 'uk', label: 'United Kingdom', icon: 'flag' },
    { value: 'de', label: 'Germany', icon: 'flag' },
    { value: 'fr', label: 'France', icon: 'flag' },
    { value: 'jp', label: 'Japan', icon: 'flag' },
    { value: 'au', label: 'Australia', icon: 'flag' },
    { value: 'br', label: 'Brazil', icon: 'flag' },
    { value: 'in', label: 'India', icon: 'flag' },
    { value: 'cn', label: 'China', icon: 'flag' }
  ];

  // Programming languages with detailed info
  programmingLanguages = [
    { value: 'javascript', label: 'JavaScript', icon: 'code', year: 1995 },
    { value: 'typescript', label: 'TypeScript', icon: 'code', year: 2012 },
    { value: 'python', label: 'Python', icon: 'code', year: 1991 },
    { value: 'java', label: 'Java', icon: 'code', year: 1995 },
    { value: 'csharp', label: 'C#', icon: 'code', year: 2000 },
    { value: 'go', label: 'Go', icon: 'code', year: 2009 },
    { value: 'rust', label: 'Rust', icon: 'code', year: 2010 },
    { value: 'swift', label: 'Swift', icon: 'code', year: 2014 }
  ];

  // Team members with roles
  teamMembers = [
    { value: 'john', label: 'John Doe', role: 'Software Engineer', icon: 'user' },
    { value: 'jane', label: 'Jane Smith', role: 'Product Manager', icon: 'user' },
    { value: 'bob', label: 'Bob Johnson', role: 'UX Designer', icon: 'user' },
    { value: 'alice', label: 'Alice Brown', role: 'Data Scientist', icon: 'user' },
    { value: 'charlie', label: 'Charlie Wilson', role: 'DevOps Engineer', icon: 'user' },
    { value: 'diana', label: 'Diana Davis', role: 'QA Engineer', icon: 'user' }
  ];

  // Status options
  statusOptions = [
    { value: 'active', label: 'Active', icon: 'circle-check', color: 'green' },
    { value: 'inactive', label: 'Inactive', icon: 'x-circle', color: 'red' },
    { value: 'pending', label: 'Pending', icon: 'clock', color: 'orange' },
    { value: 'suspended', label: 'Suspended', icon: 'pause', color: 'gray' }
  ];

  // Priority levels
  priorityLevels = [
    { value: 'low', label: 'Low Priority', icon: 'chevron-down', color: 'blue' },
    { value: 'medium', label: 'Medium Priority', icon: 'minus', color: 'orange' },
    { value: 'high', label: 'High Priority', icon: 'arrow-up', color: 'red' },
    { value: 'critical', label: 'Critical', icon: 'alert-triangle', color: 'red' }
  ];

  // Large dataset for search testing
  largeDataset = Array.from({ length: 100 }, (_, i) => ({
    value: i + 1,
    label: `Item ${i + 1}`,
    category: ['Category A', 'Category B', 'Category C'][i % 3],
    icon: 'package'
  }));

  // Form values
  selectedUser = 3;
  selectedCountry = 'us';
  selectedLanguages: string[] = ['javascript', 'typescript'];
  selectedTeamMembers: string[] = [];
  selectedStatus = 'active';
  selectedPriority = 'medium';
  searchableValue = '';
  disabledValue = 'inactive';
  errorValue = '';
  customIconValue = '';
  largeDataValue = '';
  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      userId: [1, Validators.required],
      countryId: ['us', Validators.required],
      status: ['', Validators.required],
      priority: ['medium'],
      searchable: [''],
      customIcon: [''],
      largeData: ['']
    });

    this.multiSelectForm = this.fb.group({
      languages: [['javascript'], Validators.required],
      teamMembers: [[], Validators.minLength(1)]
    });
  }

  selectionChange(event: any) {
    console.log("Selection changed:", event);
  }

  multiSelectionChange(event: any) {
    console.log("Multi-selection changed:", event);
  }

  submit() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }
    console.log('Form submitted:', this.form.value);
  }

  submitMultiSelect() {
    if (this.multiSelectForm.invalid) {
      this.multiSelectForm.markAllAsTouched();
      return;
    }
    console.log('Multi-select form submitted:', this.multiSelectForm.value);
  }

  getFieldError(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
    }
    return '';
  }

  getMultiSelectFieldError(fieldName: string): string {
    const field = this.multiSelectForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `Please select at least ${field.errors['minlength'].requiredLength} item(s)`;
      }
    }
    return '';
  }

  // Trigger error state for demonstration
  triggerError() {
    this.form.get('status')?.setErrors({ required: true });
    this.form.get('status')?.markAsTouched();
  }

  clearError() {
    this.form.get('status')?.setErrors(null);
  }

  // Reset forms
  resetForms() {
    this.form.reset({
      userId: 1,
      countryId: 'us',
      status: '',
      priority: 'medium',
      searchable: '',
      customIcon: '',
      largeData: ''
    });

    this.multiSelectForm.reset({
      languages: ['javascript'],
      teamMembers: []
    });

    // Reset component values
    this.selectedUser = 3;
    this.selectedCountry = 'us';
    this.selectedLanguages = ['javascript', 'typescript'];
    this.selectedTeamMembers = [];
    this.selectedStatus = 'active';
    this.selectedPriority = 'medium';
    this.searchableValue = '';
    this.disabledValue = 'inactive';
    this.errorValue = '';
    this.customIconValue = '';
    this.largeDataValue = '';
  }
}
