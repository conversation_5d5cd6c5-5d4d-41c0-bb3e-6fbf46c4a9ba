import { Component } from '@angular/core';
import { SelectComponent } from '../../../../../play-comp-library/src/lib/components/select/select.component';
import { SelectOptionComponent } from '../../../../../play-comp-library/src/lib/components/select/select-option/select-option.component';
import { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule, NgFor } from '@angular/common';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ButtonComponent, IconComponent } from 'play-comp-library';
import { CheckboxComponent } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-app-select',
  imports: [CommonModule, IconComponent, ButtonComponent, FormsModule, CheckboxComponent, ReactiveFormsModule, NgFor, SelectComponent, SelectOptionComponent],
  templateUrl: './app-select.component.html',
  styleUrl: './app-select.component.scss'
})
export class AppSelectComponent {
  form: FormGroup;
  users = [
    { value: 1, label: 'John' },
    { value: 2, label: 'Jan' },
    { value: 3, label: 'Jack' },
    { value: 4, label: 'James' }
  ];
  selectedUser = 3;
  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      userId: [1, Validators.required]  // initialize with a default if needed
    });
  }

  selectionChange(event: any) {
    console.log("event", event);
  }

  submit() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }
    console.log(this.form.valid)
  }


  getFieldError(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
    }
    return '';
  }
}
