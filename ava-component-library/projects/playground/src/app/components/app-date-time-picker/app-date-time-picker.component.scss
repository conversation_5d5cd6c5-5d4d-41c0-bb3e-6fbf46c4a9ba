.date-time-picker-demo {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  .demo-header {
    text-align: center;
    margin-bottom: 48px;

    h1 {
      color: #111827;
      font-size: 40px;
      margin-bottom: 16px;
      font-weight: 600;
    }

    .description {
      color: #6b7280;
      font-size: 18px;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  .demo-sections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 48px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 32px;
    }
  }

  .demo-section {
    h2 {
      color: #111827;
      font-size: 24px;
      margin-bottom: 8px;
      font-weight: 600;
    }

    p {
      color: #6b7280;
      margin-bottom: 24px;
      line-height: 1.5;
      font-size: 16px;
    }

    .demo-container {
      display: flex;
      justify-content: center;
      margin-bottom: 32px;
    }

    .selection-info {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 24px;

      h3 {
        color: #111827;
        font-size: 18px;
        margin-bottom: 16px;
        font-weight: 600;
      }

      .info-item {
        color: #6b7280;
        margin-bottom: 8px;
        line-height: 1.5;
        font-size: 14px;

        strong {
          color: #111827;
          font-weight: 600;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}