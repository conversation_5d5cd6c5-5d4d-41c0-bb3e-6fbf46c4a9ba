<div class="date-time-picker-demo">
  <div class="demo-header">
    <h1>Date Time Picker Component</h1>
    <p class="description">
      A composite component that combines calendar and time picker functionality for complete date-time selection.
    </p>
  </div>

  <div class="demo-sections">
    <!-- Basic Date Time Picker -->
    <div class="demo-section">
      <h2>Basic Date Time Picker</h2>
      <p>Select a single date and time</p>

      <div class="demo-container">
        <ava-date-time-picker
          [alwaysOpen]="true"
          [isRange]="false"
          (dateTimeSelected)="onBasicDateTimeSelected($event)"
          (dateSelected)="onBasicDateSelected($event)"
          (timeSelected)="onTimeSelected($event)">
        </ava-date-time-picker>
      </div>

      <div class="selection-info" *ngIf="selectedDate || selectedTime">
        <h3>Selected Values:</h3>
        <div class="info-item" *ngIf="selectedDate">
          <strong>Date:</strong> {{ selectedDate | date:'fullDate' }}
        </div>
        <div class="info-item" *ngIf="selectedTime">
          <strong>Time:</strong> {{ selectedTime }}
        </div>
      </div>
    </div>

    <!-- Range Date Time Picker -->
    <div class="demo-section">
      <h2>Range Date Time Picker</h2>
      <p>Select a date range and time</p>

      <div class="demo-container">
        <ava-date-time-picker
          [alwaysOpen]="true"
          [isRange]="true"
          (dateTimeSelected)="onRangeDateTimeSelected($event)"
          (rangeSelected)="onRangeSelected($event)"
          (timeSelected)="onTimeSelected($event)">
        </ava-date-time-picker>
      </div>

      <div class="selection-info" *ngIf="selectedRange.start || selectedRange.end || selectedRangeTime">
        <h3>Selected Values:</h3>
        <div class="info-item" *ngIf="selectedRange.start">
          <strong>Start Date:</strong> {{ selectedRange.start | date:'fullDate' }}
        </div>
        <div class="info-item" *ngIf="selectedRange.end">
          <strong>End Date:</strong> {{ selectedRange.end | date:'fullDate' }}
        </div>
        <div class="info-item" *ngIf="selectedRangeTime">
          <strong>Time:</strong> {{ selectedRangeTime }}
        </div>
      </div>
    </div>
  </div>
</div>
