import { Component } from '@angular/core';
import { DataGridComponent } from '../../../../../play-comp-library/src/lib/components/data-grid/data-grid.component';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonComponent } from '../../../../../play-comp-library/src/public-api';
import { CommonModule } from '@angular/common';
import { AvaColumnDefDirective } from '../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-cell-def.directive';
@Component({
  selector: 'app-app-data-grid',
  imports: [CommonModule, FormsModule, ReactiveFormsModule, ButtonComponent,
    DataGridComponent, AvaColumnDefDirective, AvaHeaderCellDefDirective, AvaCellDefDirective],
  templateUrl: './app-data-grid.component.html',
  styleUrl: './app-data-grid.component.scss'
})
export class AppDataGridComponent {
  formGroups: FormGroup[] = [];
  column = ['name', 'age']
  data = [
    { name: 'Ashok', age: 30 },
    { name: 'Nikita', age: 28 },
    { name: 'bond', age: 35 },
    { name: 'decapriyo', age: 40 },
    { name: 'Vin', age: 45 },
    { name: 'walker', age: 32 }

  ];
  constructor(private fb: FormBuilder) { }
  ngOnInit() {
    this.createFormGroups(this.data);
    console.log('Data loaded into table:', this.data);
  }
  basic() {
    console.log(this.data);
  }
  createFormGroups(data: any[]) {
    this.formGroups = this.data.map(row =>
      this.fb.group({
        name: [row.name],
        age: [row.age]
      })
    );
  }

  getFormData() {
    console.log("formGroups", this.formGroups);
  }

  get combinedFormValue(): any[] {
    return this.formGroups.map(group => group.value);
  }

  onDataSorted(sortedData: any[]) {
    this.data = sortedData;
    this.createFormGroups(sortedData);
  }
  getControl(i: number, controlName: string): FormControl {
    return this.formGroups[i].get(controlName) as FormControl;
  }
}
