<div class="button-demo-page">
    <!-- Header -->
    <div class="demo-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="box container">
                        <h1>Basic</h1>
                        <ava-data-grid [dataSource]="data" [displayedColumns]="['name', 'age']">
                            <ng-container avaColumnDef="name" [sortable]="true">
                                <th *avaHeaderCellDef> Name </th>
                                <td *avaCellDef="let row"> {{ row.name }} </td>
                            </ng-container>
                            <ng-container avaColumnDef="age">
                                <th *avaHeaderCellDef> Age </th>
                                <td *avaCellDef="let row"> {{ row.age }} </td>
                            </ng-container>
                        </ava-data-grid>
                    </div>

                    <div class="box">
                        <h1>NgModel</h1>
                        <ava-data-grid [dataSource]="data" [displayedColumns]="['name', 'age']">
                            <ng-container avaColumnDef="name" [sortable]="true">
                                <th *avaHeaderCellDef> Name </th>
                                <td *avaCellDef="let row"> {{ row.name }} </td>
                            </ng-container>

                            <ng-container avaColumnDef="age">
                                <th *avaHeaderCellDef> Age </th>
                                <td *avaCellDef="let row">
                                    <input [(ngModel)]="row.age" />
                                </td>
                            </ng-container>
                        </ava-data-grid>
                        {{data | json}}

                    </div>

                    <div class="box">
                        <h1>Form Control</h1>
                        <ava-data-grid [dataSource]="data" [displayedColumns]="['name', 'age']"
                            (dataSorted)="onDataSorted($event)">
                            <ng-container avaColumnDef="name" [sortable]="true">
                                <th *avaHeaderCellDef> Name </th>
                                <td *avaCellDef="let row; let i = index">
                                    <input [formControl]="getControl(i, 'name')" />
                                </td>
                            </ng-container>

                            <ng-container avaColumnDef="age">
                                <th *avaHeaderCellDef> Age </th>
                                <td *avaCellDef="let row; let i = index">
                                    <input [formControl]="getControl(i, 'age')" />
                                </td>
                            </ng-container>
                        </ava-data-grid>


                        <pre>{{combinedFormValue | json}}</pre>
                        <ava-button label="Submit" variant="primary" (userClick)="getFormData()"></ava-button>

                    </div>
                </div>
            </div>
        </div>
    </div>